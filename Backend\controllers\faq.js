const ErrorResponse = require('../utils/errorResponse');
const FAQ = require('../models/FAQ');

// @desc    Get all active FAQs (Public)
// @route   GET /api/faqs
// @access  Public
exports.getActiveFAQs = async (req, res, next) => {
  try {
    const { category, limit = 50 } = req.query;

    // Build query for active FAQs only
    const query = { status: 'active' };
    
    if (category) {
      query.category = category;
    }

    // Get active FAQs sorted by order
    const faqs = await FAQ.find(query)
      .select('question answer category order')
      .sort({ order: 1, createdAt: 1 })
      .limit(parseInt(limit));

    res.status(200).json({
      success: true,
      data: faqs,
      count: faqs.length
    });
  } catch (error) {
    console.error('Get active FAQs error:', error);
    next(new ErrorResponse('Failed to fetch FAQs', 500));
  }
};

// @desc    Get FAQ categories (Public)
// @route   GET /api/faqs/categories
// @access  Public
exports.getFAQCategories = async (req, res, next) => {
  try {
    // Get distinct categories from active FAQs
    const categories = await FAQ.distinct('category', { status: 'active' });

    res.status(200).json({
      success: true,
      data: categories
    });
  } catch (error) {
    console.error('Get FAQ categories error:', error);
    next(new ErrorResponse('Failed to fetch FAQ categories', 500));
  }
};

// @desc    Search FAQs (Public)
// @route   GET /api/faqs/search
// @access  Public
exports.searchFAQs = async (req, res, next) => {
  try {
    const { q: searchTerm, category, limit = 20 } = req.query;

    if (!searchTerm || searchTerm.trim().length < 2) {
      return next(new ErrorResponse('Search term must be at least 2 characters long', 400));
    }

    // Build query
    const query = {
      status: 'active',
      $or: [
        { question: { $regex: searchTerm.trim(), $options: 'i' } },
        { answer: { $regex: searchTerm.trim(), $options: 'i' } }
      ]
    };

    if (category) {
      query.category = category;
    }

    // Search FAQs
    const faqs = await FAQ.find(query)
      .select('question answer category order')
      .sort({ order: 1, createdAt: 1 })
      .limit(parseInt(limit));

    res.status(200).json({
      success: true,
      data: faqs,
      count: faqs.length,
      searchTerm: searchTerm.trim()
    });
  } catch (error) {
    console.error('Search FAQs error:', error);
    next(new ErrorResponse('Failed to search FAQs', 500));
  }
};
