/* FAQ Section Styles */
.faq-section {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
  overflow: hidden;
}

.faq-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23e2e8f0" stroke-width="0.5" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.5;
  pointer-events: none;
}

.faq-container {
  position: relative;
  z-index: 1;
}

/* Header */
.faq-header {
  text-align: center;
  margin-bottom: 3rem;
}

.faq-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 1rem;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.faq-subtitle {
  font-size: 1.125rem;
  color: var(--text-muted);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Controls */
.faq-controls {
  margin-bottom: 2.5rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  align-items: center;
}

.faq-search {
  position: relative;
  width: 100%;
  max-width: 500px;
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  font-size: 1.25rem;
  pointer-events: none;
}

.search-input {
  width: 100%;
  padding: 0.875rem 1rem 0.875rem 3rem;
  border: 2px solid var(--light-gray);
  border-radius: 50px;
  font-size: 1rem;
  background: var(--white);
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

.faq-categories {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  justify-content: center;
}

.category-btn {
  padding: 0.5rem 1.25rem;
  border: 2px solid var(--light-gray);
  border-radius: 25px;
  background: var(--white);
  color: var(--text-dark);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: capitalize;
}

.category-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-2px);
}

.category-btn.active {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: var(--white);
  box-shadow: 0 4px 12px rgba(var(--primary-rgb), 0.3);
}

/* FAQ List */
.faq-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-width: 800px;
  margin: 0 auto;
}

.faq-item {
  background: var(--white);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid var(--light-gray);
}

.faq-item:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.faq-question {
  width: 100%;
  padding: 1.5rem;
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
  position: relative;
}

.faq-question:hover {
  background: rgba(var(--primary-rgb), 0.02);
}

.faq-question.active {
  background: rgba(var(--primary-rgb), 0.05);
  border-bottom: 1px solid var(--light-gray);
}

.faq-question-text {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-dark);
  line-height: 1.5;
  flex: 1;
}

.faq-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  background: var(--primary-color);
  color: var(--white);
  font-size: 1.25rem;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.faq-question:hover .faq-icon {
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(var(--primary-rgb), 0.3);
}

.faq-answer {
  overflow: hidden;
}

.faq-answer-content {
  padding: 0 1.5rem 1.5rem;
  color: var(--text-muted);
  line-height: 1.7;
  font-size: 1rem;
}

/* Loading and Error States */
.faq-loading,
.faq-error {
  text-align: center;
  padding: 3rem 1rem;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--light-gray);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.faq-error p {
  color: var(--error-color, #ef4444);
  font-size: 1.125rem;
}

.no-faqs {
  text-align: center;
  padding: 2rem;
  color: var(--text-muted);
  font-size: 1.125rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .faq-title {
    font-size: 2rem;
  }

  .faq-subtitle {
    font-size: 1rem;
  }

  .faq-controls {
    gap: 1rem;
  }

  .faq-search {
    max-width: 100%;
  }

  .search-input {
    padding: 0.75rem 1rem 0.75rem 2.75rem;
    font-size: 0.875rem;
  }

  .search-icon {
    font-size: 1.125rem;
    left: 0.875rem;
  }

  .category-btn {
    padding: 0.375rem 1rem;
    font-size: 0.8125rem;
  }

  .faq-question {
    padding: 1.25rem;
    gap: 0.75rem;
  }

  .faq-question-text {
    font-size: 1rem;
  }

  .faq-icon {
    width: 1.75rem;
    height: 1.75rem;
    font-size: 1.125rem;
  }

  .faq-answer-content {
    padding: 0 1.25rem 1.25rem;
    font-size: 0.9375rem;
  }
}

@media (max-width: 480px) {
  .faq-title {
    font-size: 1.75rem;
  }

  .faq-question {
    padding: 1rem;
  }

  .faq-question-text {
    font-size: 0.9375rem;
  }

  .faq-answer-content {
    padding: 0 1rem 1rem;
    font-size: 0.875rem;
  }

  .category-btn {
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
  }
}
