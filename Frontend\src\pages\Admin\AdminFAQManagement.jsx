import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  selectUI,
  selectLoading,
  selectErrors,
  addActivity,
} from "../../redux/slices/adminDashboardSlice";
import { showSuccess, showError } from "../../utils/toast";
import AdminLayout from "../../components/admin/AdminLayout";
import Table from "../../components/common/Table";
import AdminTableActions from "../../components/admin/AdminTableActions";
import AdminPagination from "../../components/admin/AdminPagination";
import ConfirmationModal from "../../components/common/ConfirmationModal";
import FAQDetailModal from "../../components/admin/FAQDetailModal";
import FAQEditorModal from "../../components/admin/FAQEditorModal";
import { adminFAQService } from "../../services/faqService";
import "../../styles/AdminFAQManagement.css";

// Icons
import {
  FaQuestionCircle,
  FaSearch,
  FaFilter,
  FaEye,
  FaEdit,
  FaTrash,
  FaPlus,
  FaToggleOn,
  FaToggleOff,
  FaSort,
} from "react-icons/fa";

const AdminFAQManagement = () => {
  const dispatch = useDispatch();
  const ui = useSelector(selectUI);
  const loading = useSelector(selectLoading);
  const errors = useSelector(selectErrors);

  // Local state
  const [faqs, setFaqs] = useState([]);
  const [selectedFaqs, setSelectedFaqs] = useState([]);
  const [filters, setFilters] = useState({
    page: 1,
    limit: 10,
    search: "",
    status: "",
    category: "",
    sortBy: "order",
    sortOrder: "asc",
  });
  const [pagination, setPagination] = useState({
    current: 1,
    pages: 1,
    total: 0,
    limit: 10,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showEditorModal, setShowEditorModal] = useState(false);
  const [selectedFAQ, setSelectedFAQ] = useState(null);
  const [editorMode, setEditorMode] = useState("create"); // "create" or "edit"
  const [confirmModal, setConfirmModal] = useState({
    isOpen: false,
    type: "",
    faq: null,
    faqIds: [],
  });

  // Fetch FAQs
  const fetchFAQs = async (params = filters) => {
    try {
      setIsLoading(true);
      const response = await adminFAQService.getAllFAQs(params);
      setFaqs(response.data || []);
      setPagination(response.pagination || {});
    } catch (error) {
      console.error("Error fetching FAQs:", error);
      showError("Failed to fetch FAQs");
    } finally {
      setIsLoading(false);
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchFAQs();
  }, []);

  // Refetch when filters change
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchFAQs(filters);
    }, 300); // Debounce search

    return () => clearTimeout(timeoutId);
  }, [filters]);

  // Handle filter changes
  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: key === "page" ? value : 1, // Reset to page 1 unless changing page
    }));
  };

  // Handle search
  const handleSearch = (e) => {
    handleFilterChange("search", e.target.value);
  };

  // Handle page change
  const handlePageChange = (page) => {
    handleFilterChange("page", page);
  };

  // Handle FAQ actions
  const handleFAQAction = (faq, action) => {
    switch (action) {
      case "view":
        setSelectedFAQ(faq);
        setShowDetailModal(true);
        break;
      case "edit":
        setSelectedFAQ(faq);
        setEditorMode("edit");
        setShowEditorModal(true);
        break;
      case "delete":
        setConfirmModal({
          isOpen: true,
          type: "single",
          faq: faq,
          faqIds: [],
        });
        break;
      case "toggle":
        handleToggleFAQStatus(faq);
        break;
      default:
        break;
    }
  };

  // Handle create FAQ
  const handleCreateFAQ = () => {
    setSelectedFAQ(null);
    setEditorMode("create");
    setShowEditorModal(true);
  };

  // Handle toggle FAQ status
  const handleToggleFAQStatus = async (faq) => {
    try {
      const newStatus = faq.status === "active" ? "inactive" : "active";
      await adminFAQService.updateFAQStatus(faq._id, newStatus);
      showSuccess(`FAQ ${newStatus === "active" ? "activated" : "deactivated"} successfully`);
      fetchFAQs();
      dispatch(addActivity({
        type: "faq_status_updated",
        description: `FAQ "${faq.question}" ${newStatus === "active" ? "activated" : "deactivated"}`,
        timestamp: new Date().toISOString(),
      }));
    } catch (error) {
      console.error("Error toggling FAQ status:", error);
      showError("Failed to update FAQ status");
    }
  };

  // Handle delete FAQ
  const handleDeleteFAQ = async () => {
    try {
      if (confirmModal.type === "single") {
        await adminFAQService.deleteFAQ(confirmModal.faq._id);
        showSuccess("FAQ deleted successfully");
        dispatch(addActivity({
          type: "faq_deleted",
          description: `FAQ "${confirmModal.faq.question}" deleted`,
          timestamp: new Date().toISOString(),
        }));
      } else if (confirmModal.type === "bulk") {
        await adminFAQService.bulkDeleteFAQs(confirmModal.faqIds);
        showSuccess(`${confirmModal.faqIds.length} FAQs deleted successfully`);
        dispatch(addActivity({
          type: "faqs_bulk_deleted",
          description: `${confirmModal.faqIds.length} FAQs deleted`,
          timestamp: new Date().toISOString(),
        }));
      }
      
      setConfirmModal({ isOpen: false, type: "", faq: null, faqIds: [] });
      setSelectedFaqs([]);
      fetchFAQs();
    } catch (error) {
      console.error("Error deleting FAQ(s):", error);
      showError("Failed to delete FAQ(s)");
    }
  };

  // Handle bulk delete
  const handleBulkDelete = () => {
    if (selectedFaqs.length === 0) {
      showError("Please select FAQs to delete");
      return;
    }

    setConfirmModal({
      isOpen: true,
      type: "bulk",
      faq: null,
      faqIds: selectedFaqs,
    });
  };

  // Handle FAQ save (create/edit)
  const handleFAQSave = async (faqData) => {
    try {
      if (editorMode === "create") {
        await adminFAQService.createFAQ(faqData);
        showSuccess("FAQ created successfully");
        dispatch(addActivity({
          type: "faq_created",
          description: `FAQ "${faqData.question}" created`,
          timestamp: new Date().toISOString(),
        }));
      } else {
        await adminFAQService.updateFAQ(selectedFAQ._id, faqData);
        showSuccess("FAQ updated successfully");
        dispatch(addActivity({
          type: "faq_updated",
          description: `FAQ "${faqData.question}" updated`,
          timestamp: new Date().toISOString(),
        }));
      }
      
      setShowEditorModal(false);
      setSelectedFAQ(null);
      fetchFAQs();
    } catch (error) {
      console.error("Error saving FAQ:", error);
      showError(`Failed to ${editorMode} FAQ`);
    }
  };

  // Format date
  const formatDate = (date) => {
    return new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Get status badge class
  const getStatusBadge = (status) => {
    return status === "active" ? "status-badge active" : "status-badge inactive";
  };

  // Table columns
  const tableColumns = [
    {
      key: "question",
      label: "Question",
      render: (faq) => (
        <div className="faq-question-cell">
          <span className="question-text" title={faq.question}>
            {faq.question.length > 80 
              ? `${faq.question.substring(0, 80)}...` 
              : faq.question}
          </span>
          {faq.category && (
            <span className="category-tag">{faq.category}</span>
          )}
        </div>
      ),
    },
    {
      key: "order",
      label: "Order",
      render: (faq) => (
        <span className="order-badge">{faq.order}</span>
      ),
    },
    {
      key: "status",
      label: "Status",
      render: (faq) => (
        <div className="status-toggle">
          <span className={getStatusBadge(faq.status)}>
            {faq.status}
          </span>
          <button
            className="toggle-btn"
            onClick={() => handleFAQAction(faq, "toggle")}
            title={`${faq.status === "active" ? "Deactivate" : "Activate"} FAQ`}
          >
            {faq.status === "active" ? (
              <FaToggleOn className="toggle-on" />
            ) : (
              <FaToggleOff className="toggle-off" />
            )}
          </button>
        </div>
      ),
    },
    {
      key: "createdAt",
      label: "Created",
      render: (faq) => formatDate(faq.createdAt),
    },
    {
      key: "actions",
      label: "Actions",
      render: (faq) => (
        <AdminTableActions
          item={faq}
          onView={() => handleFAQAction(faq, "view")}
          onEdit={() => handleFAQAction(faq, "edit")}
          onDelete={() => handleFAQAction(faq, "delete")}
          permissions={{
            view: true,
            edit: true,
            delete: true,
          }}
          tooltips={{
            view: "View FAQ",
            edit: "Edit FAQ",
            delete: "Delete FAQ",
          }}
        />
      ),
      className: "actions-column",
    },
  ];

  return (
    <AdminLayout>
      <div className="AdminFAQManagement">
        {/* Header */}
        <div className="AdminFAQManagement__header">
          <div className="header-content">
            <div className="header-info">
              <h1 className="page-title">
                <FaQuestionCircle className="title-icon" />
                FAQ Management
              </h1>
              <p className="page-subtitle">
                Manage frequently asked questions for your platform
              </p>
            </div>
            <button
              className="btn-primary create-btn"
              onClick={handleCreateFAQ}
            >
              <FaPlus />
              Create FAQ
            </button>
          </div>
        </div>

        {/* Filters */}
        <div className="AdminFAQManagement__filters">
          <div className="filters-row">
            <div className="search-box">
              <FaSearch className="search-icon" />
              <input
                type="text"
                placeholder="Search FAQs..."
                value={filters.search}
                onChange={handleSearch}
                className="search-input"
              />
            </div>

            <div className="filter-group">
              <select
                value={filters.status}
                onChange={(e) => handleFilterChange("status", e.target.value)}
                className="filter-select"
              >
                <option value="">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>

              <select
                value={filters.category}
                onChange={(e) => handleFilterChange("category", e.target.value)}
                className="filter-select"
              >
                <option value="">All Categories</option>
                <option value="general">General</option>
                <option value="billing">Billing</option>
                <option value="technical">Technical</option>
                <option value="account">Account</option>
              </select>

              <select
                value={`${filters.sortBy}-${filters.sortOrder}`}
                onChange={(e) => {
                  const [sortBy, sortOrder] = e.target.value.split("-");
                  handleFilterChange("sortBy", sortBy);
                  handleFilterChange("sortOrder", sortOrder);
                }}
                className="filter-select"
              >
                <option value="order-asc">Order (Low to High)</option>
                <option value="order-desc">Order (High to Low)</option>
                <option value="createdAt-desc">Newest First</option>
                <option value="createdAt-asc">Oldest First</option>
                <option value="question-asc">Question (A-Z)</option>
                <option value="question-desc">Question (Z-A)</option>
              </select>
            </div>
          </div>

          {selectedFaqs.length > 0 && (
            <div className="bulk-actions">
              <span className="selected-count">
                {selectedFaqs.length} FAQ(s) selected
              </span>
              <button
                className="btn-danger bulk-delete-btn"
                onClick={handleBulkDelete}
              >
                <FaTrash />
                Delete Selected
              </button>
            </div>
          )}
        </div>

        {/* FAQ Table */}
        <div className="AdminFAQManagement__table">
          <Table
            columns={tableColumns}
            data={faqs}
            isAdmin={true}
            loading={{
              isLoading: isLoading,
              message: "Loading FAQs...",
            }}
            emptyMessage={
              <div className="no-results">
                <FaQuestionCircle className="no-results-icon" />
                <h3>No FAQs found</h3>
                <p>Try adjusting your search or filter criteria</p>
              </div>
            }
            className="faqs-table"
          />
        </div>

        {/* Pagination */}
        <AdminPagination
          currentPage={pagination.current}
          totalPages={pagination.pages}
          totalItems={pagination.total}
          itemsPerPage={pagination.limit}
          onPageChange={handlePageChange}
          isLoading={isLoading}
          className="admin-faqs-pagination"
        />

        {/* Modals */}
        {showDetailModal && (
          <FAQDetailModal
            faq={selectedFAQ}
            isOpen={showDetailModal}
            onClose={() => {
              setShowDetailModal(false);
              setSelectedFAQ(null);
            }}
          />
        )}

        {showEditorModal && (
          <FAQEditorModal
            faq={selectedFAQ}
            mode={editorMode}
            isOpen={showEditorModal}
            onClose={() => {
              setShowEditorModal(false);
              setSelectedFAQ(null);
            }}
            onSave={handleFAQSave}
          />
        )}

        <ConfirmationModal
          isOpen={confirmModal.isOpen}
          title={
            confirmModal.type === "single"
              ? "Delete FAQ"
              : "Delete Multiple FAQs"
          }
          message={
            confirmModal.type === "single"
              ? `Are you sure you want to delete the FAQ "${confirmModal.faq?.question}"? This action cannot be undone.`
              : `Are you sure you want to delete ${confirmModal.faqIds.length} selected FAQ(s)? This action cannot be undone.`
          }
          confirmText="Delete"
          cancelText="Cancel"
          onConfirm={handleDeleteFAQ}
          onCancel={() =>
            setConfirmModal({ isOpen: false, type: "", faq: null, faqIds: [] })
          }
          variant="danger"
        />
      </div>
    </AdminLayout>
  );
};

export default AdminFAQManagement;
