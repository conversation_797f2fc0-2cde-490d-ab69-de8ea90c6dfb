import React, { useState, useEffect } from "react";
import { FaTimes, FaQuestionCircle, FaSave, FaPlus, FaEdit } from "react-icons/fa";
import "../../styles/FAQEditorModal.css";

const FAQEditorModal = ({ faq, mode, isOpen, onClose, onSave }) => {
  const [formData, setFormData] = useState({
    question: "",
    answer: "",
    category: "general",
    status: "active",
    order: 0,
  });
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize form data when modal opens or FAQ changes
  useEffect(() => {
    if (isOpen) {
      if (mode === "edit" && faq) {
        setFormData({
          question: faq.question || "",
          answer: faq.answer || "",
          category: faq.category || "general",
          status: faq.status || "active",
          order: faq.order || 0,
        });
      } else {
        setFormData({
          question: "",
          answer: "",
          category: "general",
          status: "active",
          order: 0,
        });
      }
      setErrors({});
    }
  }, [isOpen, mode, faq]);

  if (!isOpen) return null;

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));

    // Clear error for this field
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.question.trim()) {
      newErrors.question = "Question is required";
    } else if (formData.question.length > 500) {
      newErrors.question = "Question cannot exceed 500 characters";
    }

    if (!formData.answer.trim()) {
      newErrors.answer = "Answer is required";
    } else if (formData.answer.length > 2000) {
      newErrors.answer = "Answer cannot exceed 2000 characters";
    }

    if (formData.category && formData.category.length > 100) {
      newErrors.category = "Category cannot exceed 100 characters";
    }

    if (formData.order < 0) {
      newErrors.order = "Order cannot be negative";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      await onSave({
        question: formData.question.trim(),
        answer: formData.answer.trim(),
        category: formData.category.trim() || "general",
        status: formData.status,
        order: parseInt(formData.order) || 0,
      });
    } catch (error) {
      console.error("Error saving FAQ:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const isEdit = mode === "edit";
  const title = isEdit ? "Edit FAQ" : "Create New FAQ";
  const icon = isEdit ? <FaEdit /> : <FaPlus />;

  return (
    <div className="modal-overlay" onClick={handleOverlayClick}>
      <div className="FAQEditorModal">
        <div className="modal-header">
          <div className="header-content">
            <FaQuestionCircle className="header-icon" />
            <h2 className="modal-title">
              {icon}
              {title}
            </h2>
          </div>
          <button className="close-btn" onClick={onClose} aria-label="Close modal">
            <FaTimes />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="modal-body">
          {/* Question */}
          <div className="form-group">
            <label htmlFor="question" className="form-label required">
              Question
            </label>
            <textarea
              id="question"
              name="question"
              value={formData.question}
              onChange={handleInputChange}
              placeholder="Enter the FAQ question..."
              className={`form-textarea ${errors.question ? "error" : ""}`}
              rows={3}
              maxLength={500}
              required
            />
            <div className="form-meta">
              <span className="char-count">
                {formData.question.length}/500 characters
              </span>
              {errors.question && (
                <span className="error-message">{errors.question}</span>
              )}
            </div>
          </div>

          {/* Answer */}
          <div className="form-group">
            <label htmlFor="answer" className="form-label required">
              Answer
            </label>
            <textarea
              id="answer"
              name="answer"
              value={formData.answer}
              onChange={handleInputChange}
              placeholder="Enter the FAQ answer..."
              className={`form-textarea ${errors.answer ? "error" : ""}`}
              rows={6}
              maxLength={2000}
              required
            />
            <div className="form-meta">
              <span className="char-count">
                {formData.answer.length}/2000 characters
              </span>
              {errors.answer && (
                <span className="error-message">{errors.answer}</span>
              )}
            </div>
          </div>

          {/* Category and Status Row */}
          <div className="form-row">
            <div className="form-group">
              <label htmlFor="category" className="form-label">
                Category
              </label>
              <select
                id="category"
                name="category"
                value={formData.category}
                onChange={handleInputChange}
                className={`form-select ${errors.category ? "error" : ""}`}
              >
                <option value="general">General</option>
                <option value="billing">Billing</option>
                <option value="technical">Technical</option>
                <option value="account">Account</option>
                <option value="support">Support</option>
              </select>
              {errors.category && (
                <span className="error-message">{errors.category}</span>
              )}
            </div>

            <div className="form-group">
              <label htmlFor="status" className="form-label">
                Status
              </label>
              <select
                id="status"
                name="status"
                value={formData.status}
                onChange={handleInputChange}
                className="form-select"
              >
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>
          </div>

          {/* Order */}
          <div className="form-group">
            <label htmlFor="order" className="form-label">
              Display Order
            </label>
            <input
              type="number"
              id="order"
              name="order"
              value={formData.order}
              onChange={handleInputChange}
              placeholder="0"
              className={`form-input ${errors.order ? "error" : ""}`}
              min="0"
              step="1"
            />
            <div className="form-help">
              Lower numbers appear first. Use 0 for automatic ordering.
            </div>
            {errors.order && (
              <span className="error-message">{errors.order}</span>
            )}
          </div>
        </form>

        <div className="modal-footer">
          <button
            type="button"
            className="btn-secondary"
            onClick={onClose}
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="btn-primary"
            onClick={handleSubmit}
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <div className="spinner"></div>
                {isEdit ? "Updating..." : "Creating..."}
              </>
            ) : (
              <>
                <FaSave />
                {isEdit ? "Update FAQ" : "Create FAQ"}
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default FAQEditorModal;
