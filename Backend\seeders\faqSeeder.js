/**
 * FAQ Seeder for XOSportsHub
 *
 * This seeder automatically runs when the server starts and creates default FAQs
 * if none exist in the database. It can also be run manually with different options.
 *
 * Usage:
 * 1. Automatic (runs on server start): FAQs are created only if none exist
 * 2. Manual (preserves existing): npm run seed:faqs
 * 3. Manual (clears existing): npm run seed:faqs:force
 *
 * The seeder creates 10 default FAQs covering general, billing, and account topics.
 */

const mongoose = require('mongoose');
const FAQ = require('../models/FAQ');
const User = require('../models/User');

// Default FAQ data
const defaultFAQs = [
  {
    question: "What is XOSportsHub.com?",
    answer: "XOSportsHub.com is an innovative digital sports playbook marketplace that allows users to buy and sell all types of winning sports-related digital content.",
    category: "general",
    order: 1,
    status: "active"
  },
  {
    question: "What types of content can I sell on the platform?",
    answer: "Sellers can list various types of digital content, including winning sports plays, premium analytics, training plans, coaching secrets, and other related materials, such as personalized sports diets, to help athletes perform better.",
    category: "general",
    order: 2,
    status: "active"
  },
  {
    question: "Can I request personalized content as a buyer?",
    answer: "Yes, you can request personalized content tailored to your specific needs.",
    category: "general",
    order: 3,
    status: "active"
  },
  {
    question: "Why should I purchase sports play videos or documents from your platform instead of free content available elsewhere?",
    answer: "Our platform offers high-quality, curated sports plays created by verified coaches—professionals who are truly qualified and experienced in their sport. Unlike generic or unverified content, our material is tailored specifically to your needs, and you can request custom plays or coaching advice for a personalized experience. This focus on expert-driven, customizable content ensures you receive valuable, actionable strategies designed to help you improve your game and achieve your goals.",
    category: "general",
    order: 4,
    status: "active"
  },
  {
    question: "Does XOSportsHub.com favor any particular sport?",
    answer: "At XOSportsHub.com, we appreciate the distinctive qualities and communities that each sport brings to the table. Our platform welcomes enthusiasts from all backgrounds—whether you're a parent encouraging your child's interest in soccer, an avid basketball player, or a devoted football fan. We cater to everyone, from beginners to seasoned coaches and athletes, and we value the rich diversity of sports. No matter your skill level or passion, XOSportsHub.com is your resource for sharing, selling, and discovering digital content that enhances your engagement with your favorite games.",
    category: "general",
    order: 5,
    status: "active"
  },
  {
    question: "Are there any fees associated with using the platform?",
    answer: "Yes, we may charge fees for listing, selling, or for premium memberships. Details regarding fees can be found on our site.",
    category: "billing",
    order: 6,
    status: "active"
  },
  {
    question: "How are transactions processed?",
    answer: "Payments are processed securely via our platform. Sellers can set fixed prices or offer their Listings through auction formats, allowing buyers to place bids. Buyers agree to pay either the listed amount or the winning bid amount, plus any applicable fees. An instant download of purchased content is provided immediately after payment.",
    category: "billing",
    order: 7,
    status: "active"
  },
  {
    question: "Who owns the content I sell?",
    answer: "Sellers retain ownership of their content but grant us a license to display, distribute, and sell it on the platform.",
    category: "account",
    order: 8,
    status: "active"
  },
  {
    question: "What rights do buyers have after purchasing content?",
    answer: "Buyers acquire only the rights to download and use the content for personal use.",
    category: "account",
    order: 9,
    status: "active"
  },
  {
    question: "How can I leave a review for a seller?",
    answer: "After completing a purchase, you will have the option to leave a review and rating for the seller. Please ensure that your review is truthful and respectful.",
    category: "general",
    order: 10,
    status: "active"
  }
];

const seedFAQs = async () => {
  try {
    console.log('🌱 Starting FAQ seeding...');

    // Find an admin user to assign as creator
    const adminUser = await User.findOne({ role: 'admin' });

    if (!adminUser) {
      console.error('❌ No admin user found. Waiting for admin user creation...');
      // Wait a bit and try again
      await new Promise(resolve => setTimeout(resolve, 1000));
      const retryAdminUser = await User.findOne({ role: 'admin' });
      if (!retryAdminUser) {
        console.error('❌ Still no admin user found. Skipping FAQ seeding.');
        return;
      }
      console.log(`📝 Found admin user on retry: ${retryAdminUser.firstName} ${retryAdminUser.lastName}`);
    } else {
      console.log(`📝 Found admin user: ${adminUser.firstName} ${adminUser.lastName}`);
    }

    const userToUse = adminUser || await User.findOne({ role: 'admin' });

    // Don't clear existing FAQs when called from server startup
    // Only create if no FAQs exist
    const existingFAQCount = await FAQ.countDocuments();
    if (existingFAQCount > 0) {
      console.log(`📝 Found ${existingFAQCount} existing FAQs, skipping seeding`);
      return;
    }

    // Create FAQs with admin user as creator
    const faqsToCreate = defaultFAQs.map(faq => ({
      ...faq,
      createdBy: userToUse._id,
      updatedBy: userToUse._id
    }));

    const createdFAQs = await FAQ.insertMany(faqsToCreate);

    console.log(`✅ Successfully created ${createdFAQs.length} FAQs`);
    console.log('📊 FAQ breakdown by category:');

    // Count FAQs by category
    const categories = {};
    createdFAQs.forEach(faq => {
      categories[faq.category] = (categories[faq.category] || 0) + 1;
    });

    Object.entries(categories).forEach(([category, count]) => {
      console.log(`   - ${category}: ${count} FAQs`);
    });

    console.log('🎉 FAQ seeding completed successfully!');

  } catch (error) {
    console.error('❌ Error seeding FAQs:', error);
    // Don't throw error when called from server startup to avoid crashing the server
    if (require.main === module) {
      throw error;
    }
  }
};

// Manual seeder function that clears existing FAQs
const seedFAQsManual = async () => {
  try {
    console.log('🌱 Starting manual FAQ seeding (will clear existing FAQs)...');

    // Find an admin user to assign as creator
    const adminUser = await User.findOne({ role: 'admin' });

    if (!adminUser) {
      console.error('❌ No admin user found. Please create an admin user first.');
      return;
    }

    console.log(`📝 Found admin user: ${adminUser.firstName} ${adminUser.lastName}`);

    // Clear existing FAQs for manual seeding
    await FAQ.deleteMany({});
    console.log('🗑️  Cleared existing FAQs');

    // Create FAQs with admin user as creator
    const faqsToCreate = defaultFAQs.map(faq => ({
      ...faq,
      createdBy: adminUser._id,
      updatedBy: adminUser._id
    }));

    const createdFAQs = await FAQ.insertMany(faqsToCreate);

    console.log(`✅ Successfully created ${createdFAQs.length} FAQs`);
    console.log('📊 FAQ breakdown by category:');

    // Count FAQs by category
    const categories = {};
    createdFAQs.forEach(faq => {
      categories[faq.category] = (categories[faq.category] || 0) + 1;
    });

    Object.entries(categories).forEach(([category, count]) => {
      console.log(`   - ${category}: ${count} FAQs`);
    });

    console.log('🎉 Manual FAQ seeding completed successfully!');

  } catch (error) {
    console.error('❌ Error in manual FAQ seeding:', error);
    throw error;
  }
};

// Run seeder if called directly
if (require.main === module) {
  const connectDB = require('../config/db');

  const runSeeder = async () => {
    try {
      await connectDB();

      // Check if --force flag is provided to clear existing FAQs
      const forceFlag = process.argv.includes('--force');

      if (forceFlag) {
        await seedFAQsManual();
      } else {
        await seedFAQs();
      }

      process.exit(0);
    } catch (error) {
      console.error('❌ Seeder failed:', error);
      process.exit(1);
    }
  };

  runSeeder();
}

module.exports = seedFAQs;
module.exports.seedFAQsManual = seedFAQsManual;
